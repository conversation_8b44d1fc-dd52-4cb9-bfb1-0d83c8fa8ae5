const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const authController = require('./auth.controller');
const { User } = require('../models');
const { userService } = require('../services');
const { pick } = require('../utils/pick');

const mongodbQueryUserPropsToRemove = '-password -__v';

const onboardStudent = catchAsync(async (req, res) => {
  const { user, access, refresh } = await userService.onboardStudent(req.user, req.body, req.files);
  const tokens = { access, refresh };

  res.status(httpStatus.OK).json({
    status: 'SUCCESS',
    message: 'Onboarding is successful.',
    data: { tokens, user: { ...authController.getSelectedUserInfo(user) } },
  });
});

const getUserProfile = catchAsync(async (req, res) => {
  let isOwner = true;
  if (req?.params?.id && req?.params?.id !== req.user?._id.toString()) isOwner = false;
  const user = await userService.getUserProfile(req?.params?.id || req?.user?._id, isOwner);

  return res
    .status(httpStatus.OK)
    .json({ data: user, message: 'User profile record retrieved successfully', status: 'SUCCESS' });
});

const getUserById = catchAsync(async (req, res) => {
  const userId = req?.params?.id;

  const user = await userService.getUserById(userId, req.user?._id.toString() === userId);

  if (!user) {
    return res.status(httpStatus.BAD_REQUEST).json({ message: 'User record not found', status: 'FAILED' });
  }

  // TODO, ONLY ADMINS SHOULD BE ABLE TO FETCH ANY DATA
  // if (userId !== req?.userId) {
  //   return res.status(httpStatus.OK).json({ data: user, message: 'User record retrieved successfully', status: 'SUCCESS' });
  // }

  return res.status(httpStatus.OK).json({ data: user, message: 'User record retrieved successfully', status: 'SUCCESS' });
});

const getUserMetaData = catchAsync(async (req, res) => {
  const data = await userService.getUserMetaData(req.user);
  return res.status(httpStatus.OK).json({ message: 'User metadata retrieved', data, status: 'SUCCESS' });
});

const getAllUsers = catchAsync(async (req, res) => {
  const users = await User.find().select(`${mongodbQueryUserPropsToRemove}`).lean();
  return res.status(httpStatus.OK).json({ data: users, message: 'Users records retrieved successfully', status: 'SUCCESS' });
});

const updateUser = catchAsync(async (req, res) => {
  if (String(req.user._id) !== req.params.id) {
    return res
      .status(httpStatus.BAD_REQUEST)
      .json({ message: 'You do not have permission perform operation', status: 'SUCCESS' });
  }
  await userService.updateUserById(req.params.id, req.body, req.files);
  return res.status(httpStatus.OK).json({ message: `User record updated`, status: 'SUCCESS' });
});

const changeUserPassword = catchAsync(async (req, res) => {
  const userId = req?.params?.id;
  await userService.changeUserPassword(userId, req.body);
  return res.status(httpStatus.OK).json({ message: `user password changed`, status: 'SUCCESS' });
});

const deleteUser = catchAsync(async (req, res) => {
  const userId = req?.params?.id;
  const deletedUser = await userService.deleteUserById(userId, req.body);
  res.status(httpStatus.OK).json({ message: `Username ${deletedUser.username} with ID ${deletedUser._id} deleted` });
});

const followUser = catchAsync(async (req, res) => {
  if (req.user._id === req?.params?.id) {
    return res.status(httpStatus.BAD_REQUEST).json({ message: 'Cannot perform operation', status: 'SUCCESS' });
  }
  await userService.followUser(req.user, req?.params?.id);
  return res.status(httpStatus.OK).json({ message: 'Successful', status: 'SUCCESS' });
});

const countFollowership = catchAsync(async (req, res) => {
  const data = await userService.countFollowership(req?.params?.id);
  return res.status(httpStatus.OK).json({ message: 'Followership counts retrieved', data, status: 'SUCCESS' });
});

const getFollowsInfo = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const data = await userService.getFollows(req?.query?.type, req?.params?.id, options);
  return res.status(httpStatus.OK).json({ data, message: 'records retrieved successfully', status: 'SUCCESS' });
});

const getMutuals = catchAsync(async (req, res) => {
  const data = await userService.getMutuals(req.user._id);
  return res.status(httpStatus.OK).json({ message: 'Mutuals retrieved', data, status: 'SUCCESS' });
});

const blockUser = catchAsync(async (req, res) => {
  const status = await userService.blockUser(req.user, req.params.id);

  return res.status(httpStatus.OK).json({ message: `User ${status}`, status: 'SUCCESS' });
});

const getDeletedUsers = catchAsync(async (req, res) => {
  const users = await userService.getDeletedUsers(req.user._id);
  return res
    .status(httpStatus.OK)
    .json({ data: users, message: 'Deleted users records retrieved successfully', status: 'SUCCESS' });
});

const getFollowSuggestions = catchAsync(async (req, res) => {
  const users = await userService.getFollowSuggestions(req.user._id, req.query.limit);
  return res.status(httpStatus.OK).json({ data: users, message: 'Follow suggestions retrieved', status: 'SUCCESS' });
});

module.exports = {
  changeUserPassword,
  getUserById,
  getAllUsers,
  updateUser,
  deleteUser,
  onboardStudent,
  getUserProfile,
  followUser,
  countFollowership,
  getFollowsInfo,
  getMutuals,
  blockUser,
  getUserMetaData,
  getDeletedUsers,
  getFollowSuggestions,
};
