/* eslint-disable security/detect-child-process */
/* eslint-disable security/detect-non-literal-fs-filename */
const httpStatus = require('http-status');
const Async = require('async');
const mongoose = require('mongoose');
// const ffmpeg = require('fluent-ffmpeg');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const ApiError = require('../utils/ApiError');
const activityLogService = require('./activity.log.service');
const notificationService = require('./notification.service');
const { uploadAndSaveMultipleBlobs, deleteManyFiles } = require('./azure.file.service');
const { reactToAnItem } = require('./shared');
const { pick, removeUndefinedKeys } = require('../utils/pick');
const { Post, File, User, Group, Comment, Reaction } = require('../models');
// const { capitalizeFirstChar } = require('../utils/stringFormatting');
const { azureContainers } = require('../config/constants');
const { notificationTypes } = require('./shared/notification.handler');
const validateId = require('./shared/validateId');
const logger = require('../config/logger');
const { emit } = require('./sockets/socket.shared');
const { postValidation } = require('../validations');
const { deleteFolder, convertPopulateToLookup, convertSelectToProject } = require('../utils/helperMethods');
const commentService = require('./comment.service');
const jobSchedulers = require('../utils/jobs/jobSchedulers');

const deletablePaths = new Set();

const deleteDeletablePaths = async () => {
  await Promise.all(
    [...deletablePaths].map(async (deletablePath) => {
      await deleteFolder(deletablePath);
      deletablePaths.delete(deletablePath);
    }),
  );
};

const emitPostToUsers = async (post, eventName = '/post-broadcast', socketConnectionParam = undefined) => {
  // eslint-disable-next-line global-require
  const socketConnection = socketConnectionParam || require('./sockets').socketConnection;
  const message = Array.isArray(post) ? post : { ...post.toJSON() };
  await emit(message, undefined, eventName, socketConnection, !post?.group);
  // This will work so long as Post model serves only for feed posts and group posts
  // TODO: If group post, broadcast to group members

  if (post.group) {
    const group = await Group.findById(post.group._id);
    const groupMembers = [...group.members, group.admin, ...group.coAdmins];
    await emit(message, groupMembers, eventName, socketConnection);
  }
};

const basicUserPostPopulate = {
  select: 'firstName lastName photo _id business online tagLine',
  populate: [{ path: 'photo', select: 'url', from: 'files' }],
  from: 'users',
};

const postOptions = {
  populate: [
    {
      path: 'media',
      populate: [{ path: 'thumbnail', select: 'url -_id', from: 'files' }],
      select: 'url thumbnail metadata -_id',
      from: 'files',
      isArray: true,
    },
    { path: 'user', ...basicUserPostPopulate },
    {
      path: 'parentPost',
      populate: [
        { path: 'user', ...basicUserPostPopulate },
        {
          path: 'media',
          populate: { path: 'thumbnail', select: 'url -_id', from: 'files' },
          select: 'url thumbnail metadata -_id',
          from: 'files',
          isArray: true,
        },
      ],
      select: 'user media _id text visibility createdAt',
      from: 'posts',
    },
    {
      path: 'group',
      select: 'name type profilePhoto _id admin coAdmins members',
      populate: { path: 'profilePhoto', select: 'url -_id', from: 'files' },
      from: 'groups',
    },
    // { path: 'comments', populate: { path: 'user', select: 'firstName lastName username photo _id tagLine' } },
    // { path: 'reposts', ...basicUserPostPopulate },
    // { path: 'repostWithThought', ...basicUserPostPopulate },
    { path: 'reactions', select: 'user type', from: 'reactions', isArray: true },
  ],
  // select: '-likes -bookmarks -comments -tags -reposts -repostWithThought ',
  select: 'group text media reactions allCommentsCount parentPost visibility edited createdAt updatedAt',
  from: 'posts',
};

const emitCommentToUsers = async (comment, eventName, socketConnectionParam = undefined) => {
  // eslint-disable-next-line global-require
  const socketConnection = socketConnectionParam || require('./sockets').socketConnection;
  const comments = Array.isArray(comment) ? comment : [comment.toJSON()];
  const postIds = [...new Set(comments.map((c) => String(c.post)))];
  if (postIds.length > 1) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'All comments must be for the same post');
  }
  if (postIds.length === 1) {
    const post = await Post.findById(postIds[0]).populate('group');
    if (post.group) {
      const members = [...post.group.members, post.group.admin, ...post.group.coAdmins];
      await emit(comments, members, eventName, socketConnection);
    } else {
      await emit(comments, undefined, eventName, socketConnection, !post.group);
    }
  }
  // This will work without issues as long as Post model continues to serve only for feed posts and group posts
};

const handleRepost = async (dataParam) => {
  const data = dataParam;
  // Handle repost with comment
  if (data.parentPostId) {
    const parentPostId = validateId(data.parentPostId, 'Post');
    const parentPost = await Post.findById(parentPostId);
    if (!parentPost) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Quoted post not found');
    }
    if (!data.text && !parentPost.text && parentPost.media.length < 1) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Parent post must have content');
    }
    // if (parentPost.reposts.includes(data.user)) {
    //   throw new ApiError(httpStatus.BAD_REQUEST, 'You have already quoted this post');
    // }
    if (parentPost.group && parentPost.group.toString() !== data.group) {
      throw new ApiError(httpStatus.FORBIDDEN, "You don't have permission to quote post.");
    }

    // Confirm that user is a member of the group
    if (data.group) {
      const group = await Group.findById(data.group);
      if (!group) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Group not found');
      }
      const isMember = [...group.members, group.admin, ...group.coAdmins]
        .map((member) => member.toString())
        .includes(String(data.user));
      if (!isMember) {
        throw new ApiError(httpStatus.FORBIDDEN, 'You must be a member of the group to quote post');
      }
    }
    data.parentPost = parentPost._id;
    await Post.updateOne({ _id: parentPost._id }, { $addToSet: { reposts: data.user } });
  }
};

const handlePostInGroup = async (groupId, dataParam, createdPost, user) => {
  const group = groupId?._id || groupId === undefined ? groupId : await Group.findById(dataParam.group);
  const data = dataParam;
  if (data.group) {
    group.posts.push(createdPost._id);
    // add other members to catchup
    group.catchups.push(
      ...[...group.members, group.admin, ...group.coAdmins].filter((id) => id.toString() !== user._id.toString()),
    );
    const isAdmin = group.admin.toString() === user._id.toString() || group.coAdmins.includes(user._id);
    await notificationService.createGroupNotification(
      user,
      group._id,
      createdPost._id,
      isAdmin,
      notificationTypes.GROUP_POST,
      group.name,
    );
    await group.save();
  }
};

const notifyTaggedUsers = async (text, createdPost, user) => {
  // notify users tagged to a post
  const details = { resourceName: 'post', resourceId: createdPost._id };
  // extract the username attached to the @ symbol
  const mentions = text?.match(/@(\w+)/g);
  if (mentions?.length) {
    await Promise.all(
      mentions.map(async (mention) => {
        const username = mention.slice(1);
        const mentionedUser = await User.findOne({ username });
        if (mentionedUser) {
          await notificationService.createMentionNotification(user, mentionedUser, details);
        }
      }),
    );
  }
};

/**
 * WARNING:
 *  Be careful of passing variable values to the exec function and fs functions
 *  as it may lead to command injection attacks
 */

const getVideoMetadata = async (videoPath) => {
  const metadataCmd = `ffprobe -i "${videoPath}" -show_streams -select_streams v -print_format json`;
  return new Promise((resolve, reject) => {
    // eslint-disable-next-line security/detect-child-process
    exec(metadataCmd, (metadataError, metadataStdout) => {
      if (metadataError) {
        logger.error(`ffprobe error' ${metadataError.message}`);
        reject(new Error('An error occurred while processing the video'));
        return;
      }
      try {
        const metadata = JSON.parse(metadataStdout).streams[0];

        const duration = parseFloat(metadata?.duration) || undefined;
        const width = metadata.width || undefined;
        const height = metadata.height || undefined;
        const rotation = metadata.side_data_list?.[0]?.rotation || undefined;
        resolve({ duration, width, height, rotation });
      } catch (err) {
        logger.error(`Error: ${err}`);
        reject(err);
      }
    });
  });
};

const convertVideoToHLS = async (inputFilePath, videoName, outputFolder, screenshotTimeStamp) => {
  const outputM3U8 = path.join(outputFolder, 'segment.m3u8');
  const cmd = `ffmpeg -i "${inputFilePath}" -c copy -hls_time 5 -threads 0 -f hls -hls_list_size 0 "${outputM3U8}" -ss ${screenshotTimeStamp} -vframes 1 -q:v 2 "${path.join(
    outputFolder,
    `${videoName}-thumbnail.png`,
  )}"`;

  return new Promise((resolve, reject) => {
    // ffmpeg command  source: https://www.mux.com/articles/how-to-convert-mp4-to-hls-format-with-ffmpeg-a-step-by-step-guide
    // exec(`ffmpeg -i ${inputFilePath} -filter_complex "[0:v]split=3[v1][v2][v3];[v1]scale=w=1920:h=1080[v1out];[v2]scale=w=1280:h=720[v2out];[v3]scale=w=854:h=480[v3out]"`,
    // eslint-disable-next-line security/detect-child-process
    exec(cmd, (error) => {
      if (error) {
        logger.error(`FFmpeg error:' ${error.message}`);
        reject(new Error('An error occurred while processing the video'));
        return;
      }
      // read other files in the folder that is not .m3u8
      const files = fs.readdirSync(outputFolder);
      const thumbnail = files.find((file) => file.endsWith('thumbnail.png'));
      const addedFiles = files.filter((file) => !file.endsWith('m3u8') && !file.endsWith('thumbnail.png'));

      resolve({
        file: outputM3U8,
        addedFiles: addedFiles.map((file) => path.join(outputFolder, file)),
        thumbnail: thumbnail ? path.join(outputFolder, thumbnail) : null,
      });
    });
  });
};

const processPostVideo = async (videoFile, todayDate) => {
  const originalname = videoFile.originalname.replace(/\s/g, '-').replace(/[^a-zA-Z0-9-.]/g, ''); // remove spaces and special characters
  // const inputFolder = path.join(__dirname, `uploads/${todayDate}`);
  const inputFolder = path.join(__dirname, `uploads/${todayDate}-${originalname.replace(/\./g, '-')}`);
  if (!fs.existsSync(inputFolder)) {
    fs.mkdirSync(inputFolder, { recursive: true });
  }
  const inputFilePath = path.join(inputFolder, originalname);
  fs.writeFileSync(inputFilePath, videoFile.buffer);

  const outputFolder = path.join(__dirname, `processed/${todayDate}-${originalname.replace(/\./g, '-')}`);
  if (!fs.existsSync(outputFolder)) {
    fs.mkdirSync(outputFolder, { recursive: true });
  }

  // get video duration and dimensions
  const { duration, ...metadata } = await getVideoMetadata(inputFilePath);
  const screenshotTimeStamp = (duration || 0) * (10 / 100); // take screenshot at 10% of video duration

  const returnData = {
    ...(await convertVideoToHLS(inputFilePath, originalname, outputFolder, screenshotTimeStamp)),
    metadata,
  };
  // TODO
  // 1. Make output folder to have the name of the video file
  // 2. Add a job to delete the input file and output folder after 5 minutes
  // await scheduleDeleteFolder({ folder: inputFolder.split(/[\\/]/).pop() });
  // await scheduleDeleteFolder({ folder: outputFolder.split(/[\\/]/).pop() });
  deletablePaths.add(inputFolder);
  deletablePaths.add(outputFolder);

  logger.info(`Processing File "${videoFile.originalname}" completed`);
  return returnData;
};

const processPostFiles = async (files, container) => {
  const videoFiles = files.filter((file) => file.mimetype.startsWith('video'));
  const nonVideoFiles = files.filter((file) => !file.mimetype.startsWith('video'));

  const todayDate = Date.now().toString();

  const processedVideoFiles = await Promise.all(
    videoFiles.map(async (videoFile) => {
      try {
        const processedVideo = await processPostVideo(videoFile, todayDate);
        return processedVideo;
      } catch (error) {
        logger.error(`Error processing video file ${videoFile.originalname}: ${error}`);
        logger.error(error);
      }
    }),
  );

  const validProcessedVideoFiles = processedVideoFiles.filter((file) => !!file);
  const processedNonVideoFiles = nonVideoFiles.map((file) => ({ file })); // make an object to match the structure of processed video files
  // const processedFiles = [...validProcessedVideoFiles, ...processedNonVideoFiles];

  // const { processedVideoFiles, processedNonVideoFiles } = await processPostFiles(files, todayDate);
  const nonVideoFilesIds = await uploadAndSaveMultipleBlobs(processedNonVideoFiles, container);

  const videoFilesIds = await uploadAndSaveMultipleBlobs(
    validProcessedVideoFiles.map((videoFile) => {
      return { file: videoFile.file, metadata: videoFile.metadata, thumbnail: videoFile.thumbnail, isVideoFile: true };
    }),
    container,
    // `${azureContainers.posts}/${videoFiles[0].split(/[\\/]/).pop()}`,
    // todayDate,
  );
  const fileIds = [...nonVideoFilesIds, ...videoFilesIds];
  // upload other ffmpeg generated files without requiring the url from azure
  uploadAndSaveMultipleBlobs(
    processedVideoFiles.flatMap((item) => item.addedFiles || []),
    container,
    // todayDate,
  );

  return fileIds;
  // return { processedVideoFiles: validProcessedVideoFiles, processedNonVideoFiles };
};

const createPostHelper = async (data, files, author, group) => {
  await handleRepost(data);

  if (!data.parentPostId && !data.text && (!files || files.length === 0)) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Post must have text or file');
  }

  let fileIds = [];
  if (files) {
    fileIds = await processPostFiles(files, azureContainers.posts);
  }

  const postBody = { ...data, media: fileIds, parentPost: data.parentPostId, group };
  if (postBody.tags) {
    postBody.tags = Array.isArray(data.tags) ? data.tags : [data.tags];
    postBody.tags = [...new Set(postBody.tags.map((tag) => tag.toLowerCase()))];
  }
  const createdPost = await Post.create(postBody);

  await emit(
    { message: 'A post has been created', author: author._id, postId: createdPost._id, parentPost: data.parentPostId },
    undefined,
    '/notify-new-post-broadcast',
    // eslint-disable-next-line global-require
    require('./sockets').socketConnection,
    !postBody.group,
  );
  await handlePostInGroup(group, data, createdPost, author);

  await notifyTaggedUsers(data.text, createdPost, author);

  const isUnykedPost = postBody.tags?.some((tag) => /^unyked$/i.test(tag));
  const isAdminAuthor = author.roles.includes('admin');

  if (isUnykedPost && isAdminAuthor && !postBody.group) {
    await jobSchedulers.scheduleSendEmailForNewUnykEdPost({
      postId: createdPost._id,
      authorId: author._id,
    });
  }

  // remove folders after processing
  deleteDeletablePaths(); // Delete folders, don't wait for it to finish
  // ['processed', 'uploads'].map((folderName) =>
  //   fs.promises.rm(path.join(__dirname, `${folderName}/${todayDate}`), { recursive: true, force: true }),
  // );
  return createdPost;
};

const createPost = async (req) => {
  const data = { ...req.body, user: req.user._id };
  const createdPost = await createPostHelper(data, req.files, req.user, req.group);

  return createdPost;
};

// const createRepostHelper = async (postId, user) => {
//   const post = await Post.findById(postId);
//   if (!post) {
//     throw new ApiError(httpStatus.NOT_FOUND, 'Post not found');
//   }

//   // post.reposts array contains user ids of users who reposted the post without thought
//   const repostedWithoutThought = post.reposts.includes(user._id);
//   if (repostedWithoutThought) {
//     post.reposts.pull(user._id);
//     user.reposts.pull(post._id);
//   } else {
//     post.reposts.push(user._id);
//     user.reposts.push(post._id);
//     await activityLogService.logActivity(user._id, 'REPOST', post._id);
//     const details = { resourceId: post._id, resourceName: Post.modelName };
//     const recipient = await User.findById(post.user);
//     await notificationService.createRepostNotification(user, recipient, details);
//   }
//   if (post.repostWithThought.includes(user._id)) {
//     throw new ApiError(httpStatus.BAD_REQUEST, 'You already reposted with your thought.');
//   }

//   await post.save();
//   await user.save();
// };

const bookmarkPost = async (req) => {
  validateId(req.params.id, 'Post');
  const post = await Post.findById(req.params.id);
  if (!post) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Post not found');
  }

  const bookmarked = req.user.postBookmarks.includes(post._id);
  if (bookmarked) {
    req.user.postBookmarks.pull(post._id);
    post.bookmarks.pull(req.user._id);
    await activityLogService.logActivity(req.user._id, 'BOOKMARK_POST', post._id);
  } else {
    req.user.postBookmarks.push(post._id);
    post.bookmarks.push(req.user._id);
    await activityLogService.logActivity(req.user._id, 'UNBOOKMARK_POST', post._id);
  }

  await req.user.save();
  await post.save();
};

const isMemberOfPostGroup = async (post, userId) => {
  if (!post?.group) return;

  let group;

  // Handle different group object structures
  if (post.group?._id && post.group?.admin && post.group?.coAdmins && post.group?.members) {
    // Group is fully populated (from aggregation or populate)
    group = post.group;
  } else if (typeof post.group === 'string' || post.group instanceof mongoose.Types.ObjectId) {
    // Group is just an ObjectId, need to fetch it
    try {
      group = await Group.findById(post.group);
    } catch (error) {
      logger.error(`Error finding group by ID: ${post.group}`, error);
      return false;
    }
  } else if (post.group.admin && post.group.coAdmins && post.group.members) {
    // Group is an object from aggregation projection with required fields
    group = post.group;
  } else if (post.group._id) {
    // Group has _id but might be missing other fields, fetch it
    try {
      group = await Group.findById(post.group._id);
    } catch (error) {
      logger.error(`Error finding group by _id: ${post.group._id}`, error);
      return false;
    }
  } else {
    // Unknown group structure, log and return false
    logger.error(`Unknown group structure in isMemberOfPostGroup:`, post.group);
    return false;
  }

  if (!group) return false;

  const members = [group.admin, ...(group.coAdmins || []), ...(group.members || [])].map((member) => member?.toString());

  return members.includes(userId.toString());
};

const getPostById = async (postId, requestingUserId, returnIsMember = false) => {
  validateId(postId, 'Post');
  // eslint-disable-next-line global-require
  const { basicUserPopulate } = require('./user.service');

  const post = await Post.findById(postId)
    .select('text visibility allCommentsCount createdAt updatedAt')
    .populate([
      {
        path: 'media',
        populate: [{ path: 'thumbnail', select: 'url -_id' }],
        select: 'url thumbnail metadata -_id',
        from: 'files',
      },
      { path: 'user', ...basicUserPopulate },
      { path: 'reposts', ...basicUserPopulate },
      { path: 'repostWithThought', ...basicUserPopulate },
      {
        path: 'parentPost',
        populate: [
          { path: 'user', ...basicUserPopulate },
          {
            path: 'media',
            populate: [{ path: 'thumbnail', select: 'url -_id', from: 'files' }],
            select: 'url thumbnail metadata -_id',
            from: 'files',
          },
        ],
        select: 'text visibility createdAt updatedAt',
        from: 'posts',
      },
      {
        path: 'reactions',
        select: 'user type',
        from: 'reactions',
      },
      {
        path: 'group',
        select: 'name type profilePhoto _id admin coAdmins members',
        populate: { path: 'profilePhoto', select: 'url -_id', from: 'files' },
        from: 'groups',
      },
    ]);

  if (!post) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Post record not found');
  }
  if (requestingUserId && post.group && returnIsMember) {
    post._doc.isMemberOfPostGroup = await isMemberOfPostGroup(post, String(requestingUserId));
    delete post._doc.admin;
    delete post._doc.coAdmins;
    delete post._doc.members;
  }
  // post._doc.createdOn = post.createdAt;
  // post._doc.updatedOn = post.updatedAt;
  // delete post._doc.createdAt
  // delete post._doc.updatedAt;
  return post;
};

// Handles creating repost (without comment) by creating a  new post
const createRepostBySocket = async (socket, data, socketConnection) => {
  try {
    validateId(data?.postId, 'Post');

    const post = await Post.findById(data.postId);

    // const post = await Post.findOne({ user: socket.user._id, _id: data.postId });
    if (!post) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Post not found');
    }
    await createPostHelper(
      { ...data, user: socket.user._id, parentPostId: data.postId },
      undefined,
      socket.user,
      post.group,
    );
    const newPost = await getPostById(data.postId, socket.user._id, true);
    await emitPostToUsers(newPost, 'repost-broadcast', socketConnection);
  } catch (error) {
    logger.error(error);
  }
};

/**
 * Enhanced Post Ranking Algorithm
 *
 * This algorithm provides a comprehensive ranking system that considers multiple factors
 * to improve post distribution and user engagement. It addresses the issue where all posts
 * from one followed user would appear before posts from other followed users.
 *
 * Key Features:
 * 1. **Relationship-based scoring**: Following, followers, mutual connections
 * 2. **Engagement metrics**: Reactions, comments, reposts, bookmarks
 * 3. **Content quality**: Media presence, text length, edited status
 * 4. **Freshness decay**: Time-based scoring with configurable decay rates
 * 5. **Author influence**: Follower count with logarithmic scaling
 * 6. **Diversity enforcement**: Prevents same author domination
 * 7. **Randomness factor**: Adds variety to prevent predictable feeds
 *
 * The algorithm uses MongoDB aggregation pipeline for efficient computation
 * and includes configurable weights for fine-tuning.
 */

// Configurable ranking weights for fine-tuning the algorithm
const RANKING_WEIGHTS = {
  following: 2, // Weight for following someone
  followedBy: 3, // Weight for being followed by someone
  mutualConnection: 3, // Weight for mutual following
  engagement: 0.3, // Weight for engagement metrics
  hasMedia: 2, // Weight for posts with media
  textLength: 1, // Weight for posts with substantial text
  edited: 0.5, // Weight for edited posts
  authorOnline: 1, // Weight for online authors
  authorInfluence: 0.5, // Weight for author influence (follower count)
  randomness: 1, // Weight for random diversity factor
  freshness: {
    // Time-based freshness weights
    veryFresh: 5, // < 1 hour
    fresh: 3, // < 6 hours
    recent: 1, // < 24 hours
    week: 0.5, // < 1 week
    old: 0.1, // > 1 week
  },
  engagement_multipliers: {
    comments: 2, // Comments are worth more than reactions
    reposts: 1.5, // Reposts show strong engagement
    bookmarks: 1.2, // Bookmarks show intent to revisit
  },
};

/**
 * Apply author diversity penalty and random factor to prevent same author domination
 * This is a CosmosDB-compatible alternative to $setWindowFields and $rand
 *
 * @param {Array} posts - Array of posts from aggregation
 * @returns {Array} - Posts with only finalRank property added
 */
const applyAuthorDiversityPenalty = (posts) => {
  const authorPostCounts = new Map();

  return posts.map((post) => {
    const authorId = post.user?._id?.toString() || post.user?.toString();

    if (!authorId) {
      // If no author ID, return post as-is with minimal random factor
      const randomFactor = Math.random() * RANKING_WEIGHTS.randomness;
      const finalRank = post.rank + randomFactor;

      // Remove all intermediate ranking properties, keep only finalRank
      const cleanPost = { ...post };
      delete cleanPost.rank;
      delete cleanPost.now;
      delete cleanPost.isFollowing;
      delete cleanPost.isFollowedByUser;
      delete cleanPost.reactionCount;
      delete cleanPost.commentScore;
      delete cleanPost.repostCount;
      delete cleanPost.bookmarkCount;
      delete cleanPost.hasMedia;
      delete cleanPost.textLength;
      delete cleanPost.isEdited;
      delete cleanPost.authorFollowerCount;
      delete cleanPost.authorIsOnline;
      delete cleanPost.mutualConnection;
      delete cleanPost.hoursOld;
      delete cleanPost.authorInfluenceScore;
      delete cleanPost.freshnessScore;
      delete cleanPost.engagementScore;

      return { ...cleanPost, finalRank };
    }

    // Track how many posts we've seen from this author
    const currentCount = authorPostCounts.get(authorId) || 0;
    authorPostCounts.set(authorId, currentCount + 1);

    // Apply diversity penalty for subsequent posts from same author
    const diversityPenalty = currentCount > 0 ? currentCount * 0.5 : 0;

    // Add random factor for feed diversity (CosmosDB-compatible)
    const randomFactor = Math.random() * RANKING_WEIGHTS.randomness;

    const finalRank = post.rank - diversityPenalty + randomFactor;

    // Remove all intermediate ranking properties, keep only finalRank
    const cleanPost = { ...post };
    delete cleanPost.rank;
    delete cleanPost.now;
    delete cleanPost.isFollowing;
    delete cleanPost.isFollowedByUser;
    delete cleanPost.reactionCount;
    delete cleanPost.commentScore;
    delete cleanPost.repostCount;
    delete cleanPost.bookmarkCount;
    delete cleanPost.hasMedia;
    delete cleanPost.textLength;
    delete cleanPost.isEdited;
    delete cleanPost.authorFollowerCount;
    delete cleanPost.authorIsOnline;
    delete cleanPost.mutualConnection;
    delete cleanPost.hoursOld;
    delete cleanPost.authorInfluenceScore;
    delete cleanPost.freshnessScore;
    delete cleanPost.engagementScore;

    return { ...cleanPost, finalRank };
  });
};

const getPosts = async (req) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const { userId, parentPostId } = req.query;
  let filter = pick({ ...req.query, user: userId, parentPost: parentPostId }, [
    'user',
    'text',
    'group',
    'tags',
    'parentPost',
  ]);

  ['user', 'parentPost', 'group'].forEach((f) => {
    if (filter[f]) {
      const valid = validateId(filter[f], undefined, false);
      if (!valid) {
        delete filter[f];
      }
    }
  });
  if (filter.tags) {
    const tagsFilter = Array.isArray(filter.tags) ? filter.tags : [filter.tags];
    filter.tags = { $all: tagsFilter };
  }

  removeUndefinedKeys(filter); // Remove user if undefined

  const { tab } = req.query;
  const requestingUserId = req.user._id;
  const myReactions = (await Reaction.find({ user: requestingUserId, post: { $exists: true } })).map(
    (reaction) => reaction.post,
  );

  ['bookmarks', 'reposts'].forEach((key) => {
    if (tab === key) {
      filter[key] = { $in: [requestingUserId] };
    }
  });
  if (tab === 'reactions') {
    filter._id = { $in: myReactions };
  }

  // This block should come after all other filters have been applied
  if (filter.group) {
    const group = await Group.findById(filter.group);
    group.catchups.pull(req.user._id);
    await group.save();
  } else if (req.query.feedsPage === 'true') {
    const visibleGroups = await Group.find({
      $or: [{ admin: req.user._id }, { coAdmins: { $in: [req.user._id] } }, { members: { $in: [req.user._id] } }],
    });
    const nonVisibleGroups = await Group.find({
      $nor: [{ admin: req.user._id }, { coAdmins: { $in: [req.user._id] } }, { members: { $in: [req.user._id] } }],
    });
    // Add filter to include posts from user's groups or public groups
    if (visibleGroups.length > 0) {
      delete filter.group;
      filter = {
        $or: [
          {
            $and: [
              ...Object.entries(filter).map(([key, value]) => ({ [key]: value })),
              { group: { $nin: nonVisibleGroups.map((group) => group._id) } },
            ],
          },
          { group: { $in: visibleGroups.map((group) => group._id) } },
        ],
      };
    }
  }

  let result;
  if (options.sortBy) {
    result = await Post.paginate(filter, { ...options, ...postOptions });
  } else {
    // Rank posts using the user's followers and following,
    options.limit = options?.limit && parseInt(options.limit, 10) > 0 ? parseInt(options.limit, 10) : 10;
    options.page = options?.page && parseInt(options.page, 10) > 0 ? parseInt(options.page, 10) : 1;
    const skip = (options.page - 1) * options.limit;

    // Create enhanced postOptions for ranking aggregation with additional user fields
    const rankingPostOptions = {
      ...postOptions,
      populate: postOptions.populate.map((populateOption) => {
        if (populateOption.path === 'user') {
          // Enhance user populate to include additional fields for comprehensive ranking
          return {
            ...populateOption,
            select: 'firstName lastName photo _id business online followers following tagLine lastLogin lastSeen createdAt',
          };
        }
        return populateOption;
      }),
    };

    // CosmosDB-compatible ranking algorithm with multiple parameters
    // Note: Using simpler aggregation stages compatible with CosmosDB MongoDB API
    const rankingAggregation = [
      {
        $match: filter,
      },
      ...convertPopulateToLookup(
        rankingPostOptions.populate,
        undefined,
        convertSelectToProject(rankingPostOptions.select),
      )[0],
      {
        $addFields: {
          // Current time for calculations
          now: new Date(),

          // Basic relationship scores
          isFollowing: {
            $cond: {
              if: { $and: [{ $isArray: '$user.followers' }, { $in: [requestingUserId, '$user.followers'] }] },
              then: 1,
              else: 0,
            },
          },
          isFollowedByUser: {
            $cond: {
              if: { $and: [{ $isArray: '$user.following' }, { $in: [requestingUserId, '$user.following'] }] },
              then: 1,
              else: 0,
            },
          },

          // Engagement metrics (using basic operations compatible with CosmosDB)
          reactionCount: { $size: { $ifNull: ['$reactions', []] } },
          commentScore: {
            $multiply: [{ $ifNull: ['$allCommentsCount', 0] }, RANKING_WEIGHTS.engagement_multipliers.comments],
          },
          repostCount: { $size: { $ifNull: ['$reposts', []] } },
          bookmarkCount: { $size: { $ifNull: ['$bookmarks', []] } },

          // Content quality indicators
          hasMedia: {
            $cond: {
              if: { $gt: [{ $size: { $ifNull: ['$media', []] } }, 0] },
              then: RANKING_WEIGHTS.hasMedia,
              else: 0,
            },
          },
          textLength: {
            $cond: {
              if: { $gte: [{ $strLenCP: { $ifNull: ['$text', ''] } }, 50] },
              then: RANKING_WEIGHTS.textLength,
              else: 0,
            },
          },
          isEdited: {
            $cond: {
              if: { $eq: ['$edited', true] },
              then: RANKING_WEIGHTS.edited,
              else: 0,
            },
          },

          // Author influence and activity
          authorFollowerCount: { $size: { $ifNull: ['$user.followers', []] } },
          authorIsOnline: {
            $cond: {
              if: { $eq: ['$user.online', true] },
              then: RANKING_WEIGHTS.authorOnline,
              else: 0,
            },
          },

          // Mutual connections (both following each other)
          mutualConnection: {
            $cond: {
              if: {
                $and: [
                  { $isArray: '$user.followers' },
                  { $isArray: '$user.following' },
                  { $in: [requestingUserId, '$user.followers'] },
                  { $in: [requestingUserId, '$user.following'] },
                ],
              },
              then: RANKING_WEIGHTS.mutualConnection,
              else: 0,
            },
          },
        },
      },
      {
        $addFields: {
          // Time-based freshness score (simplified for CosmosDB compatibility)
          hoursOld: {
            $divide: [
              { $subtract: ['$now', '$createdAt'] },
              3600000, // milliseconds in an hour
            ],
          },

          // Author influence score (simplified - no log10 as it may not be supported)
          authorInfluenceScore: {
            $cond: {
              if: { $gt: ['$authorFollowerCount', 0] },
              then: {
                $multiply: [
                  {
                    $cond: {
                      if: { $gte: ['$authorFollowerCount', 1000] },
                      then: 3, // High influence
                      else: {
                        $cond: {
                          if: { $gte: ['$authorFollowerCount', 100] },
                          then: 2, // Medium influence
                          else: 1, // Low influence
                        },
                      },
                    },
                  },
                  RANKING_WEIGHTS.authorInfluence,
                ],
              },
              else: 0,
            },
          },
        },
      },
      {
        $addFields: {
          // Freshness decay (newer posts get higher scores)
          freshnessScore: {
            $cond: {
              if: { $lte: ['$hoursOld', 1] },
              then: RANKING_WEIGHTS.freshness.veryFresh, // Very fresh (< 1 hour)
              else: {
                $cond: {
                  if: { $lte: ['$hoursOld', 6] },
                  then: RANKING_WEIGHTS.freshness.fresh, // Fresh (< 6 hours)
                  else: {
                    $cond: {
                      if: { $lte: ['$hoursOld', 24] },
                      then: RANKING_WEIGHTS.freshness.recent, // Recent (< 24 hours)
                      else: {
                        $cond: {
                          if: { $lte: ['$hoursOld', 168] }, // 1 week
                          then: RANKING_WEIGHTS.freshness.week,
                          else: RANKING_WEIGHTS.freshness.old, // Old posts get minimal freshness score
                        },
                      },
                    },
                  },
                },
              },
            },
          },

          // Total engagement score
          engagementScore: {
            $add: [
              '$reactionCount',
              '$commentScore',
              { $multiply: ['$repostCount', RANKING_WEIGHTS.engagement_multipliers.reposts] },
              { $multiply: ['$bookmarkCount', RANKING_WEIGHTS.engagement_multipliers.bookmarks] },
            ],
          },
        },
      },
      {
        $addFields: {
          // Final comprehensive rank calculation
          rank: {
            $add: [
              // Relationship scores
              { $multiply: ['$isFollowing', RANKING_WEIGHTS.following] },
              { $multiply: ['$isFollowedByUser', RANKING_WEIGHTS.followedBy] },
              '$mutualConnection', // Already uses RANKING_WEIGHTS.mutualConnection

              // Engagement scores (scaled)
              { $multiply: ['$engagementScore', RANKING_WEIGHTS.engagement] },

              // Content quality
              '$hasMedia', // Already uses RANKING_WEIGHTS.hasMedia
              '$textLength', // Already uses RANKING_WEIGHTS.textLength
              '$isEdited', // Already uses RANKING_WEIGHTS.edited

              // Freshness
              '$freshnessScore', // Already uses RANKING_WEIGHTS.freshness values

              // Author factors
              '$authorIsOnline', // Already uses RANKING_WEIGHTS.authorOnline
              '$authorInfluenceScore', // Already uses RANKING_WEIGHTS.authorInfluence

              // Note: Random factor will be added in post-processing (CosmosDB compatible)
            ],
          },
        },
      },
      {
        $sort: {
          rank: -1,
          createdAt: -1, // Secondary sort by creation time
        },
      },
      // Note: Removed $skip and $limit to implement diversity post-processing
    ];

    // Execute aggregation without pagination for diversity processing
    let aggregationResults = await Post.aggregate(rankingAggregation);

    // Apply author diversity penalty (CosmosDB-compatible approach)
    aggregationResults = applyAuthorDiversityPenalty(aggregationResults);

    // Re-sort after applying diversity penalty
    aggregationResults.sort((a, b) => {
      if (b.finalRank !== a.finalRank) {
        return b.finalRank - a.finalRank;
      }
      return new Date(b.createdAt) - new Date(a.createdAt);
    });

    // Apply pagination after diversity sorting
    const startIndex = skip;
    const endIndex = skip + options.limit;
    const paginatedResults = aggregationResults.slice(startIndex, endIndex);

    // Create result object in expected format
    result = {
      results: paginatedResults,
      page: options.page,
      limit: options.limit,
      totalPages: Math.ceil(aggregationResults.length / options.limit),
      totalResults: aggregationResults.length,
    };
  }
  // Rename createdAt to createdOn. createdAt is auto removed by toJSON plugin

  await Async.eachOfSeries(result.results, async (post, index) => {
    if (post.group) {
      const resultPart = result.results[index]._doc || result.results[index];
      if (post.group._id) {
        resultPart.isMemberOfPostGroup = await isMemberOfPostGroup(post, requestingUserId);
        delete resultPart.group?.admin;
        delete resultPart.group?.coAdmins;
        delete resultPart.group?.members;
      } else if (!(post.group instanceof mongoose.Types.ObjectId)) {
        delete resultPart.group;
      }
    }
  });

  return result;
};

const updatePost = async (req) => {
  const { files, resourceRecord, body: updateBody } = req;
  validateId(req.params.id, 'Post');
  const post = resourceRecord || (await Post.findById(req.params.id)); // resourceRecord is from canManageResource middleware

  if (!post) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Post record not found');
  }

  if (updateBody.tags) {
    updateBody.tags = Array.isArray(updateBody.tags) ? updateBody.tags : [updateBody.tags];
    updateBody.tags = [...new Set(updateBody.tags.map((tag) => tag.toLowerCase()))];
  }

  if (updateBody.deletedUrls) {
    await Async.eachOfSeries(updateBody.deletedUrls.split(','), async (url) => {
      // Regex to get url without query params
      // eslint-disable-next-line no-useless-escape
      let urlExtract = url.match(/^(https?:\/\/[^\/\s]+\/[^\?\s]*)/);
      if (urlExtract) {
        urlExtract = urlExtract[0]?.replace(/\/$/, '');
      } else {
        logger.error(`Error extracting url: ${url}`);
        throw new ApiError(httpStatus.BAD_REQUEST, 'Error deleting media');
      }

      const media = await File.findOne({ url: urlExtract });
      if (media) {
        post.media = post.media.filter((file) => String(file._id || file) !== media._id.toString());
        await media.remove();
      }
    });
  }

  try {
    if (files && files.length > 0) {
      const fileIds = await processPostFiles(files, azureContainers.posts);
      // upload other ffmpeg generated files without requiring the url from azure

      updateBody.media = [...post.media, ...fileIds];
    }
    if (Object.keys(updateBody).filter((key) => !['visibility'].includes(key)).length > 0) {
      // Array containing "visibility" holds keys that should not count when updating as edited
      updateBody.edited = true;
    }

    Object.assign(post, updateBody);
    if (!post.text && !post.media.length) {
      logger.info(`Update post text: ${post.text}, ${post.media.length}`);
      throw new ApiError(httpStatus.BAD_REQUEST, 'Post must have text or file');
    }
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating post');
  }

  await post.save();
  // remove folders after processing
  // ['processed', 'uploads'].map((folderName) =>
  //   fs.promises.rm(path.join(__dirname, `${folderName}/${todayDate}`), { recursive: true, force: true }),
  // );
  deleteDeletablePaths(); // Delete folders, don't wait for it to finish
  return post;
};

const deletePost = async (postId) => {
  validateId(postId, 'Post');
  const post = await Post.findById(postId);
  if (!post) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Post not found');
  }

  if (post.media && post.media.length > 0) {
    await deleteManyFiles(post.media);
  }
  if (post.parentPost) {
    const parentPost = await Post.findById(post.parentPost);
    parentPost.repostWithThought.pull(post.user);
    await parentPost.save();
  }
  const users = await User.find({ $or: [{ postBookmarks: post._id }, { reposts: post._id }] });
  await Reaction.deleteMany({ post: post._id });
  await Promise.all(
    users.map(async (user) => {
      user.postBookmarks.pull(post._id);
      user.reposts.pull(post._id);
      await user.save();
    }),
  );

  const comments = await Comment.find({ post: post._id }).select('_id').lean();
  await Promise.all(comments.map((comment) => commentService.deleteCommentById(comment._id.toString(), null)));

  await post.remove();
  await emit({ message: 'A post has been deleted', postId }, undefined, 'notify-deleted-post-broadcast', undefined, true);
  return post;
};

const reactToPostHelper = async (data, user, isSocket) => {
  const { postId, commentId, reactionType } = data;

  if (postId && commentId) {
    throw new Error('You can only react to a post or a comment at a time, not both.');
  }

  let resource;
  if (postId) {
    resource = await reactToAnItem(Post, postId, user, reactionType);
    if (isSocket) await emitPostToUsers(resource, '/post-broadcast');
  } else if (commentId) {
    resource = await reactToAnItem(Comment, commentId, user, reactionType);
    const eventName = resource.parentComment ? '/comment-reply-broadcast' : '/comment-broadcast';
    if (isSocket) await emitCommentToUsers(resource, eventName);
  } else {
    throw new Error('You must provide a postId or commentId');
  }
  return resource;
};

const reactToPost = async (data, user) => {
  const resource = await reactToPostHelper(data, user);
  return resource;
};

const reactToPostBySocket = async (socket, clientData) => {
  try {
    const { value: data, error } = postValidation.reactToPostBySocket.validate(clientData);
    if (error) {
      throw new Error(`${error.details[0].message}`);
    }
    await reactToPostHelper(data, socket.user, true);
  } catch (error) {
    logger.error(error);
  }
};

const getUsersReaction = async (postId, tab) => {
  validateId(postId, 'Post');
  // eslint-disable-next-line global-require, no-shadow
  const { basicUserPopulate } = require('./user.service');
  const populatePath =
    tab === 'reactions'
      ? { path: 'reactions', select: 'user type', populate: { path: 'user', ...basicUserPopulate } }
      : [
          { path: 'reposts', ...basicUserPopulate },
          { path: 'repostWithThought', ...basicUserPopulate },
        ];
  const usersWhoReacted = await Post.findById(postId).populate(populatePath);

  const data =
    tab === 'reactions'
      ? usersWhoReacted[tab]
      : { reposts: usersWhoReacted.reposts, repostWithThought: usersWhoReacted.repostWithThought };

  return data;
};

module.exports = {
  createPost,
  bookmarkPost,
  getPostById,
  getPosts,
  updatePost,
  deletePost,
  reactToPost,
  reactToPostBySocket,
  createRepostBySocket,
  getUsersReaction,
  emitCommentToUsers,
  emitPostToUsers,
  postOptions,
  processPostFiles,
  RANKING_WEIGHTS, // Export for potential external configuration
};
