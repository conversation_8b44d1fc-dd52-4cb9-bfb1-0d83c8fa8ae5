const passport = require('passport');
const httpStatus = require('http-status');
const jwt = require('jsonwebtoken');
const ApiError = require('../utils/ApiError');
const { roleRights } = require('../config/roles');
const Models = require('../models');
const config = require('../config/config');
const logger = require('../config/logger');
const validateId = require('../services/shared/validateId');
const { registrationStatuses, allowedAuthRoutesWithoutOnboarding, businessEnums } = require('../config/constants');

const verifyCallback = (req, resolve, reject, requiredRights) => async (err, payload, info) => {
  // const { user, clientUserType } = payload;
  const { user } = payload || {};
  if (!user) {
    return reject(new ApiError(httpStatus.UNAUTHORIZED, 'Please authenticate'));
  }
  if (err) {
    logger.error(`Error authenticating user: ${err}`);
  }
  if (info) {
    logger.info(`Info from jwt authentication: ${info}`);
  }

  if (
    user.registrationStatus !== registrationStatuses.ONBOARDING_COMPLETE &&
    !allowedAuthRoutesWithoutOnboarding.some((route) => !!req.originalUrl.match(route))
  ) {
    return reject(
      new ApiError(httpStatus.PRECONDITION_FAILED, 'You need to complete onboarding', undefined, undefined, {
        user: {
          pendingStudent: user.pendingStudent,
          pendingBusiness: user.pendingBusiness,
          registrationStatus: user.registrationStatus,
        },
        code: 'ONBOARDING_REQUIRED',
      }),
    );
  }
  req.user = user;

  if (requiredRights.length) {
    const userRights = user.roles.reduce((acc, role) => [...acc, ...roleRights.get(role)], []);
    const hasRequiredRights = requiredRights.every((requiredRight) => userRights.includes(requiredRight));
    if (!hasRequiredRights && req.params.userId !== user.id) {
      return reject(new ApiError(httpStatus.FORBIDDEN, 'Forbidden'));
    }
  }

  resolve();
};

const auth =
  (...requiredRights) =>
  async (req, res, next) => {
    return new Promise((resolve, reject) => {
      passport.authenticate('jwt', { session: false }, verifyCallback(req, resolve, reject, requiredRights))(req, res, next);
    })
      .then(() => next())
      .catch((err) => next(err));
  };

// Confirm that userId(or user, whichever was used by target model) associated with a resource is the same as authenticated user
const canManageResource = (modelName) => async (req, res, next) => {
  const { [`${modelName}`]: Model } = Models; // Target model e.g Post

  // Don't query db again if the model is User. user is in req if authenticated
  // let resource;
  if (req.params?.id) validateId(req.params.id, modelName);
  const resource = modelName === 'User' ? req.user : await Model.findById(req.params.id);
  if (!resource) next(new ApiError(httpStatus.NOT_FOUND, `${modelName} not found`));

  const resourceUserId = modelName === 'User' ? resource?._id : resource?.userId || resource?.user || resource?.admin;

  if (!resource) {
    if (modelName === 'User') {
      return next(new ApiError(httpStatus.UNAUTHORIZED, 'Unauthorized: You may not be signed in'));
    }
    return next(new ApiError(httpStatus.NOT_FOUND, `${modelName} not found`));
  }
  if (resourceUserId?.toString() !== req.user._id.toString()) {
    return next(new ApiError(httpStatus.FORBIDDEN, 'Forbidden: You are not allowed to perform this operation'));
  }
  // Add the record found to the request object. It can be used in subsequent middlewares without getting by ID to reduce database queries
  req.resourceRecord = resource;
  next();
};

const socketAuth = (socketParam, next) => {
  const socket = socketParam;
  const token = socket.handshake.headers.authorization?.split(' ')[1];
  const err = new Error('Unauthorized: Invalid token');
  err.data = { status: 401, type: 'authentication_error', message: 'Unauthorized: Invalid token' };

  if (!token || token === 'undefined') {
    return next(err);
  }

  jwt.verify(token, config.jwt.secret, async (error, decoded) => {
    if (error) {
      return next(err);
    }

    const user = await Models.User.findById(decoded.sub.userId).populate({ path: 'photo', select: 'url' });
    if (!user) {
      return next(err);
    }
    socket.user = user;
    next();
  });
};

const verifyBusinessOnboardingStage = async (req, res, next) => {
  const { stage } = req.body;
  // if (!Object.values(businessEnums.onboardingStages).includes(stage)) {
  //   return res.status(400).json({ message: 'Invalid onboarding stage.' });
  // }

  if (!req.user.business && stage === businessEnums.onboardingStages.BUSINESS_INFO) next(); // first onboarding stage
  else {
    const business = await Models.Business.findById(req.user.business);
    const { onboardingStage } = business;

    const stagesOrder = Object.values(businessEnums.onboardingStages);
    if (!stage || !stagesOrder.includes(stage)) {
      return res.status(400).json({ message: 'Invalid onboarding stage.' });
    }

    const currentStageIndex = stagesOrder.indexOf(onboardingStage);
    const requestedStageIndex = stagesOrder.indexOf(stage);

    if (requestedStageIndex <= currentStageIndex) {
      return res.status(400).json({ message: 'You cannot go back to a previous stage or repeat or skip stages.' });
    }

    req.business = business;

    next();
  }
};

const verifyServiceStage = async (req, res, next) => {
  const { stage } = req.query;
  if (!stage || !Object.values(businessEnums.serviceStages).includes(stage)) {
    return res.status(400).json({ status: 'FAILED', message: 'Invalid or missing stage' });
  }
  req.stage = stage;
  next();
};

const authExcept =
  (routes, ...requiredRights) =>
  async (req, res, next) => {
    if (routes.some((route) => !!req.path.match(route))) {
      return next();
    }
    return auth(...requiredRights)(req, res, next);
  };

module.exports = {
  auth,
  authExcept,
  canManageResource,
  socketAuth,
  verifyBusinessOnboardingStage,
  verifyServiceStage,
};
