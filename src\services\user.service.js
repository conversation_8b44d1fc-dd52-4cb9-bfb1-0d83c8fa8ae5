const bcrypt = require('bcryptjs');
const httpStatus = require('http-status');
const { User, File, Setting, Account, Business, DeletedUser, Profile } = require('../models');
const { userDefaultSettings, azureContainers, registrationStatuses, clientUserTypes } = require('../config/constants');
const { uploadAzureBlob, saveFile } = require('./azure.file.service');
const { scheduleDeleteUserInfo } = require('../utils/jobs/jobSchedulers');
const validateId = require('./shared/validateId');
const ApiError = require('../utils/ApiError');
const emailService = require('./email.service');
const messageService = require('./message.service');
const profileService = require('./profile.service');
const tokenService = require('./token.service');
const notificationService = require('./notification.service');

const mongodbQueryUserPropsToRemove = '-password -__v';

const basicUserPopulate = {
  select: 'firstName lastName middleName username photo tagLine business online',
  populate: [
    { path: 'photo', select: 'url' },
    { path: 'business', select: 'verificationStatus' },
  ],
};

const emailExists = async (email) => {
  const user = await User.findOne({ email });
  return !!user;
};

const usernameExists = async (username) => {
  const user = await User.findOne({ username });
  return !!user;
};

const usernameEmailAvail = async (email, username) => {
  const user = await User.findOne({
    $or: [{ username: { $regex: username, $options: 'i' } }, { email: { $regex: email, $options: 'i' } }],
  });
  return !user;
};

const processFileUpload = async (file, containerName) => {
  const { filename, url } = await uploadAzureBlob(file, containerName);
  const savedFile = await saveFile(url, filename, containerName);
  return savedFile._id;
};

const sendVerificationEmail = async (email, userParam, signupAs, returnUrl) => {
  // Duplicate Signup is for example, a user is signed up as a business contact person and wants to signup as a student
  const user = userParam || (await User.findOne({ email }));
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
  }

  if (user.emailVerificationCount > 5) {
    await User.updateOne({ email }, { $set: { locked: true } });
    throw new ApiError(httpStatus.TOO_MANY_REQUESTS, 'Too many requests. Please, contact Support.');
  }
  await User.updateOne({ email }, { $set: { emailVerificationCount: user.emailVerificationCount + 1 } });

  const verifyEmailToken = await tokenService.generateVerifyEmailToken(user, signupAs);

  if (user.isEmailVerified) {
    throw new ApiError(httpStatus.ALREADY_REPORTED, 'Email already verified');
  } else {
    await emailService.sendVerificationEmail(user, verifyEmailToken, signupAs, returnUrl);
  }
};

const createUser = async (userBody) => {
  const existingUser = await User.isEmailTaken(userBody.email);
  // eslint-disable-next-line no-param-reassign
  userBody.username = userBody.username.toLowerCase();
  if (userBody.username.match(/[^a-zA-Z0-9]/)) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Username can only contain letters and numbers');
  }

  const isUsernameTaken = await User.isUsernameTaken(userBody.username);

  if (existingUser) {
    if (isUsernameTaken) {
      throw new ApiError(httpStatus.CONFLICT, 'Email and username are already taken');
    }
    throw new ApiError(httpStatus.CONFLICT, 'Email already taken');
  }

  if (isUsernameTaken) {
    throw new ApiError(httpStatus.CONFLICT, 'Username already taken');
  }

  return User.create(userBody);
};

const getUserProfile = async (userId, isOwner) => {
  validateId(userId);

  const user = await User.findById(userId)
    .select(
      `${
        !isOwner
          ? 'firstName lastName middleName username email photo banner profile followers following postLikes postBookmarks reposts commentLikes tagLine business online gender countryOfResidence stateOfResidence nationality preferredDegreeOfStudy preferredSchoolsOfStudy preferredCountryOfStudy preferredFieldsOfStudy'
          : ''
      }`,
    )
    .populate([
      {
        path: 'photo',
        select: 'url -_id',
      },
      {
        path: 'banner',
        select: 'url -_id',
      },
      {
        path: 'profile',
        populate: ['education', 'certification', 'testscore', 'experience', 'project', 'volunteering', 'award'],
      },
      {
        path: 'business',
        select: 'displayName username verificationStatus',
      },
    ]);

  if (isOwner) {
    return user;
  }

  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
  }

  const userProfileFiltered = await User.privacy({ resourceId: userId, userProfileRecord: user });

  return userProfileFiltered;
};

const onboardStudent = async (user, userData, files) => {
  const photos = {};
  // create profile
  if (user.registrationStatus !== registrationStatuses.ACCOUNT_VERIFIED) {
    if (user.registrationStatus === registrationStatuses.SIGNUP_COMPLETED) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Please activate your account first.');
    }
    throw new ApiError(httpStatus.BAD_REQUEST, 'Onboarding already completed.');
  }

  if (files?.photo) {
    photos.photo = await processFileUpload(files.photo[0], azureContainers.userProfilePhotos);
  }

  if (files?.banner) {
    photos.banner = await processFileUpload(files.banner[0], azureContainers.userProfileBanners);
  }

  const profile = await profileService.createProfile(user, {
    basicInformation: { personalStatement: userData.personalStatement },
  });
  const setting = await Setting.create({ ...userDefaultSettings(), user: user._id });
  Object.assign(user, {
    ...userData,
    ...photos,
    registrationStatus: registrationStatuses.ONBOARDING_COMPLETE,
    profile: profile._id,
    setting: setting._id,
    roles: Array.from(new Set([...user.roles, clientUserTypes.STUDENT])),
    pendingStudent: 'false',
  });
  await user.save();
  const { access, refresh } = await tokenService.generateAuthTokens(user);
  return { user, access, refresh };
};

// const verifyDuplicateSignupEmail = async (email, signupAs, token) => {
//   let user;
//   let returnData = {};

//   try {
//     const verifyTokenDoc = await tokenService.verifyToken(token, tokenTypes.VERIFY_EMAIL);
//     user = await User.findById(verifyTokenDoc.user._id);
//   } catch (error) {
//     user = await User.findOne({ email });
//     if (
//       user &&
//       ((signupAs === clientUserTypes.BUSINESS && !user.pendingBusiness) ||
//         (signupAs === clientUserTypes.STUDENT && !user.pendingStudent))
//     ) {
//       // Resend verification email
//       const verifyEmailToken = await tokenService.generateVerifyEmailToken(user);
//       await emailService.sendVerifyDuplicateSignupEmail(user, verifyEmailToken, signupAs);
//       throw new ApiError(httpStatus.TEMPORARY_REDIRECT, 'Link is expired. A new link has been sent to your email.');
//     }
//     throw new ApiError(httpStatus.UNAUTHORIZED, 'Verification failed. Link is invalid');
//   }

//   if (user.email !== email) {
//     throw new ApiError(httpStatus.UNAUTHORIZED, 'Inconsistent data.');
//   }

//   if (!user) {
//     throw new ApiError(httpStatus.NOT_FOUND, 'An error occurred. User record not found');
//   }

//   returnData.isEmailVerified = user.isEmailVerified;
//   if (signupAs === clientUserTypes.BUSINESS) {
//     if (user.pendingBusiness === 'true') {
//       throw new ApiError(httpStatus.CONFLICT, 'Business account already verified');
//     }
//     user.pendingBusiness = 'true';
//     returnData = { ...returnData, pendingBusiness: 'true', nextStep: 'onboard_business' };
//   } else if (signupAs === clientUserTypes.STUDENT) {
//     if (user.pendingStudent === 'true') {
//       throw new ApiError(httpStatus.CONFLICT, 'Student account already verified');
//     }
//     user.pendingStudent = 'true';
//     returnData = { ...returnData, pendingStudent: 'true', nextStep: 'onboard_student' };
//     returnData.pendingStudent = 'true';
//   }

//   await user.save();
//   return returnData;
// };

const queryUsers = async (filter, options) => {
  const users = await User.paginate(filter, options);
  return users;
};

const getUserById = async (userId, isOwner) => {
  const populate = [
    { path: 'photo', select: 'url -_id' },
    { path: 'banner', select: 'url -_id' },
  ];
  if (isOwner) {
    populate.push({
      path: 'business',
      select: 'displayName username profilePhoto coverPhoto tagLine referralCode',
      populate: [
        { path: 'profilePhoto', select: 'url' },
        { path: 'coverPhoto', select: 'url' },
      ],
    });
  }
  const user = await User.findById(userId).populate(populate).select(`${mongodbQueryUserPropsToRemove}`);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
  }
  return user;
};

const getUserByEmail = async (email, raiseError = true) => {
  const user = await User.findOne({ email: { $regex: email, $options: 'i' } }).populate([
    { path: 'photo', select: 'url -_id' },
    { path: 'banner', select: 'url -_id' },
    { path: 'business', select: 'verificationStatus' },
  ]);
  if (!user) {
    if (raiseError) {
      throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
    }
    return null;
  }
  return user;
};

const getUserByUsername = async (username) => {
  const user = User.findOne({ username: RegExp(username, 'i') }).lean();
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
  }
  return user;
};

const countFollowership = async (userId) => {
  const user = await User.findById(userId);
  const data = { followers: user.followers.length, following: user.following.length };
  return data;
};

const getUserMetaData = async (userParam) => {
  const userId = userParam._id;
  const metaDataProps =
    'account banner countryOfResidence email emailVerificationCount firstName gender isEmailVerified lastName middleName nationality phoneVerified phoneVerifyCount photo premiumSubscriber profile registrationStatus roles tagLine username';

  const getUserPromise = User.findById(userId)
    .select(`${metaDataProps}`)
    .populate([
      { path: 'photo', select: 'url -_id' },
      { path: 'banner', select: 'url -_id' },
      {
        path: 'business',
        select: 'displayName username profilePhoto coverPhoto tagLine referralCode verificationStatus',
        populate: [
          { path: 'profilePhoto', select: 'url' },
          { path: 'coverPhoto', select: 'url' },
        ],
      },
    ])
    .lean();
  const getUnreadConversationsCountPromise = messageService.countUnreadConversations(userId);
  const getFollowsCountPromise = countFollowership(userId);
  const getUnreadNoificationsCountPromise = notificationService.getNotifications({
    recipient: userId,
    read: false,
  });
  const getSettingPromise = Setting.findOne({ user: userId }).lean();

  const getProfilePromise = Profile.findById(userParam.profile).select('basicInformation').lean();

  const [user, unreadConversationsCount, followsCount, unreadNotificationsCount, setting, profile] = await Promise.all([
    getUserPromise,
    getUnreadConversationsCountPromise,
    getFollowsCountPromise,
    getUnreadNoificationsCountPromise,
    getSettingPromise,
    getProfilePromise,
  ]);

  if (user.business) {
    user.business.id = user.business._id;
    delete user.business._id;
  }
  user.id = user._id;
  delete user._id;

  return {
    user: { ...user, personalStatement: profile?.basicInformation?.personalStatement },
    unreadConversationsCount,
    followsCount,
    unreadNotificationsCount: unreadNotificationsCount.totalResults,
    theme: setting.theme,
  };
};

const updateUserById = async (userId, body, files) => {
  const updateBody = body;
  const user = await getUserById(userId);

  if (updateBody.username) {
    updateBody.username = updateBody.username.toLowerCase();
    const existingUser = await getUserByUsername(updateBody.username);
    if (existingUser && existingUser._id.toString() !== userId) {
      throw new ApiError(httpStatus.CONFLICT, 'Username already taken');
    }
  }

  if (files?.photo) {
    const file = await File.findById(updateBody.photo);
    if (file) {
      file.remove();
    }
    updateBody.photo = await processFileUpload(files.photo[0], azureContainers.userProfilePhotos);
  }

  if (files?.banner) {
    const file = await File.findById(updateBody.banner);
    if (file) {
      file.remove();
    }
    updateBody.banner = await processFileUpload(files.banner[0], azureContainers.userProfileBanners);
  }

  if (updateBody.personalStatement) {
    await Profile.updateOne(
      { _id: user.profile },
      { $set: { 'basicInformation.personalStatement': updateBody.personalStatement } },
    );
    delete updateBody.personalStatement;
  }

  // faux
  Object.assign(user, updateBody);
  await user.save();
  return user;
};

const changeUserPassword = async (userId, updateBody) => {
  const user = await User.findById(userId);
  const { oldPassword, newPassword } = updateBody;

  if (!(await user.isPasswordMatch(oldPassword))) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Old password incorrect');
  }

  const salt = await bcrypt.genSalt(10);
  const hashedPwd = await bcrypt.hash(newPassword, salt);
  user.password = hashedPwd;
  await user.save();
};

const deleteUserById = async (userId, data) => {
  const user = await User.findById(userId);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
  }

  const business = await Business.findOne({ contactPerson: userId });
  if (business) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Please delete associated business');
  }

  await scheduleDeleteUserInfo({ userId });

  const promises = [];
  if (user.profile) promises.push(profileService.deleteProfile(user.profile));
  if (user.setting) promises.push(Setting.deleteOne({ user: user._id }));
  if (user.account) promises.push(Account.deleteOne({ user: user._id }));
  if (user.photo) promises.push(File.deleteOne({ _id: user.photo }));
  if (user.banner) promises.push(File.deleteOne({ _id: user.banner }));

  promises.push(User.findByIdAndDelete(userId));

  promises.push(DeletedUser.create({ email: user.email, reason: data.reason || '', accountCreatedAt: user.createdAt }));

  await Promise.all(promises);
  return user;
};

/**
 * Follows or unfollows a user based on the specified type.
 * @param {string} actionType - Specifies the action: 'follow' or 'unfollow'.
 * @param {User} currentUser - The user performing the action.
 * @param {string} userToFollowOrUnfollowId - The ID of the user to follow or unfollow.
 */
const followUser = async (currentUser, userToFollowOrUnfollowId) => {
  if (currentUser._id.toString() === userToFollowOrUnfollowId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'You cannot follow yourself');
  }

  if (currentUser.following.includes(userToFollowOrUnfollowId)) {
    // if you're already following the user, then unfollow
    const userToUnfollow = await getUserById(userToFollowOrUnfollowId);

    // eslint-disable-next-line no-param-reassign
    currentUser.following = currentUser.following.filter((id) => id.toString() !== userToFollowOrUnfollowId);
    await currentUser.save();

    userToUnfollow.followers = userToUnfollow.followers.filter((id) => id.toString() !== currentUser._id.toString());
    await userToUnfollow.save();
  } else if (!currentUser.following.includes(userToFollowOrUnfollowId)) {
    const userToFollow = await getUserById(userToFollowOrUnfollowId);

    currentUser.following.push(userToFollow._id);
    await currentUser.save();

    userToFollow.followers.push(currentUser._id);
    await userToFollow.save();

    await notificationService.createFollowNotification(currentUser, userToFollow);
  }
};

const getFollows = async (type, userId, options) => {
  const { page, limit } = options;
  const skip = (page - 1) * limit;
  // type is either 'followers' or 'following'
  const user = await User.findById(userId).populate([
    {
      path: type,
      select: 'firstName lastName middleName tagLine followers following username countryOfResidence stateOfResidence',
      options: { skip, limit },
      populate: [
        {
          path: 'photo',
          select: 'url -_id',
        },
        {
          path: 'banner',
          select: 'url -_id',
        },
      ],
    },
  ]);
  if (!user) throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
  const totalCount = type === 'followers' ? user.followers.length : user.following.length;
  const data = {
    data: user[type],
    page,
    limit,
    totalPages: Math.ceil(totalCount / limit),
    totalResults: totalCount,
  };
  return data;
};

const getMutuals = async (userId) => {
  const { followers, following: followings } = await User.findById(userId).populate([
    {
      path: 'followers',
      select: 'firstName lastName middleName username',
      populate: [
        {
          path: 'photo',
          select: 'url -_id',
        },
      ],
    },
    {
      path: 'following',
      select: 'firstName lastName middleName username',
      populate: [
        {
          path: 'photo',
          select: 'url -_id',
        },
      ],
    },
  ]);

  const mutuals = followers.filter((follower) =>
    followings.some((following) => following._id.toString() === follower._id.toString()),
  );
  return mutuals;
};

const blockUser = async (userProfile, userToBlockId) => {
  validateId(userToBlockId);
  const { blockedUsers } = userProfile;
  let status = 'blocked';
  if (!blockedUsers.includes(userToBlockId)) {
    Object.assign(userProfile, { ...userProfile, blockedUsers: [...blockedUsers, userToBlockId] });
  } else {
    Object.assign(userProfile, {
      ...userProfile,
      blockedUsers: blockedUsers.filter((id) => id.toString() !== String(userToBlockId)),
    });
    status = 'unblocked';
  }

  await userProfile.save();
  return status;
};

const getDeletedUsers = async () => {
  const deletedUsers = await DeletedUser.find({}).lean();
  return deletedUsers;
};

const getFollowSuggestions = async (userId, optionsParam) => {
  const options = optionsParam;
  options.limit = options?.limit && parseInt(options.limit, 10) > 0 ? parseInt(options.limit, 10) : 10;
  options.page = options?.page && parseInt(options.page, 10) > 0 ? parseInt(options.page, 10) : 1;
  const skip = (options.page - 1) * options.limit;

  const followSuggestionsAggregate = [
    {
      $match: {
        isEmailVerified: true,
        locked: false,
        registrationStatus: registrationStatuses.ONBOARDING_COMPLETE,
      },
    },

    {
      $sort: {
        rank: -1,
        createdAt: -1,
      },
    },
    { $skip: skip },
    { $limit: options.limit },
  ];
  const suggestedUsers = await User.aggregate(followSuggestionsAggregate);
  return suggestedUsers;
};

module.exports = {
  changeUserPassword,
  createUser,
  onboardStudent,
  queryUsers,
  getUserById,
  getUserByEmail,
  getUserByUsername,
  updateUserById,
  deleteUserById,
  sendVerificationEmail,
  // verifyDuplicateSignupEmail,
  getUserProfile,
  emailExists,
  usernameExists,
  usernameEmailAvail,
  followUser,
  getFollows,
  countFollowership,
  getMutuals,
  basicUserPopulate,
  blockUser,
  getUserMetaData,
  getDeletedUsers,

  getFollowSuggestions,
};
