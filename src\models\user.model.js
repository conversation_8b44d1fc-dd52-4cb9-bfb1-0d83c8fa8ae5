const mongoose = require('mongoose');
const validator = require('validator');
const bcrypt = require('bcryptjs');
const { toJSON, paginate, privacy } = require('./plugins');
const { genderTypes, registrationStatuses } = require('../config/constants');
const { roles } = require('../config/roles');

const userSchema = new mongoose.Schema(
  {
    profile: { type: mongoose.Schema.Types.ObjectId, ref: 'Profile' },
    setting: { type: mongoose.Schema.Types.ObjectId, ref: 'Setting' },
    firstName: {
      type: String,
      required: true,
      trim: true,
    },

    lastName: {
      type: String,
      // required: true,
      trim: true,
    },

    middleName: {
      type: String,
      trim: true,
    },

    username: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
    },

    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
      validate(value) {
        if (!validator.isEmail(value)) {
          throw new Error('Invalid email');
        }
      },
    },

    password: {
      type: String,
      // required: true,
      trim: true,
      minlength: 8,
      validate(value) {
        if (!value.match(/\d/) || !value.match(/[a-zA-Z]/)) {
          throw new Error('Password must contain at least one letter and one number');
        }
      },
      private: true, // used by the toJSON plugin
    },

    photo: { type: mongoose.Schema.Types.ObjectId, ref: 'File' },
    banner: { type: mongoose.Schema.Types.ObjectId, ref: 'File' },
    nationality: { type: String, trim: true },
    phone: { type: String, trim: true },
    phoneVerified: { type: Boolean, default: false },
    earliestPhoneVerifyRequest: { type: Date, default: Date.now }, // Earliest time user could request phone verification. When the value is the undefined, user cannot request phone verification
    phoneVerifyCount: { type: Number, default: 0 }, // Next gap to be given before resending phone otp (in minutes)
    gender: { type: String, trim: true, enum: Object.values(genderTypes) },

    countryOfResidence: {
      type: String,
      trim: true,
    },

    stateOfResidence: {
      type: String,
      trim: true,
    },

    targetedCountries: {
      type: [String],
      required: true,
      trim: true,
    },

    preferredFieldsOfStudy: {
      type: [String],
      trim: true,
    },

    preferredSchoolsOfStudy: { type: [String], trim: true },

    preferredCountryOfStudy: {
      type: String,
      trim: true,
    },

    preferredDegreeOfStudy: {
      type: String,
      trim: true,
    },

    dateOfBirth: {
      type: Date,
      trim: true,
    },

    interestedInScholarship: {
      type: Boolean,
      default: false,
    },

    interestedInGrant: {
      type: Boolean,
      default: false,
    },

    recoveryEmail: {
      type: String,
      required: false,
      sparse: true,
      trim: true,
      lowercase: true,
      validator(value) {
        return value === null || validator.isRecoveryEmailTaken(value);
      },
    },

    postBookmarks: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Post' }],
    reposts: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Post' }],
    postLikes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Post' }],
    commentLikes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Comment' }],
    reactions: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Reaction' }],
    savedUniversities: [{ type: mongoose.Schema.Types.ObjectId, ref: 'University' }],
    appliedUniversities: [{ type: mongoose.Schema.Types.ObjectId, ref: 'University' }],
    hiddenUniversities: [{ type: mongoose.Schema.Types.ObjectId, ref: 'University' }],

    roles: {
      type: [String],
      // default: ['student'],
      enum: Object.values(roles),
      lowercase: true,
    },

    tagLine: {
      type: String,
      trim: true,
    },

    isEmailVerified: { type: Boolean, default: false }, // To be replaced by pendingStudent and pendingBusiness. Deprecated.
    registrationStatus: {
      type: String,
      default: registrationStatuses.SIGNUP_COMPLETED,
      enum: Object.values(registrationStatuses),
    },
    followers: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }], // user that the user is following
    following: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }], // users followed by the user
    locked: { type: Boolean, default: false },
    lastLogin: { type: Date, default: Date.now },
    online: { type: Boolean },
    lastSeen: { type: Date },
    emailVerificationCount: { type: Number, default: 0 },
    pendingPasswordResetCount: { type: Number, default: 0 },
    business: { type: mongoose.Schema.Types.ObjectId, ref: 'Business' },
    pendingBusiness: { type: String, enum: ['true', 'false'] }, // Business hasn't completed onboarding
    pendingStudent: { type: String, enum: ['true', 'false'] }, // Student hasn't completed onboarding. undefined if not a student, true if student hasn't completed onboarding but has verified email, false if student has completed onboarding
    signupMedium: { type: String, enum: ['email', 'google', 'facebook', 'linkedin'], default: 'email' },
    savedServices: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Service' }],
    recentlyViewedServices: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Service' }],
    stripeCustomerId: { type: String },
    referral: { type: mongoose.Schema.Types.ObjectId, ref: 'Referral' }, // who referred this user
    // referrals: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Referral' }], // users referred by this user
    scholarships: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Scholarship' }],

    account: { type: mongoose.Schema.Types.ObjectId, ref: 'Account' },

    blockedUsers: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    premiumSubscriber: { type: mongoose.Schema.Types.ObjectId, ref: 'PremiumSubscriber' },
  },
  {
    timestamps: true,
  },
);

userSchema.index({ createdAt: 1 });
userSchema.index({ updatedAt: 1 });
userSchema.index({ firstName: 1, lastName: 1 });

userSchema.plugin(toJSON);
userSchema.plugin(paginate);
userSchema.plugin(privacy);

userSchema.statics.isEmailTaken = async function (email, excludeUserId) {
  const user = await this.findOne({ email: { $regex: email, $options: 'i' }, _id: { $ne: excludeUserId } });
  return user;
};

userSchema.statics.isRecoveryEmailTaken = async function (recoveryEmail, excludeUserId) {
  const user = await this.findOne({ recoveryEmail: { $regex: recoveryEmail, $options: 'i' }, _id: { $ne: excludeUserId } });
  return !!user;
};

userSchema.statics.isUsernameTaken = async function (username, excludeUserId) {
  const user = await this.findOne({ username: { $regex: username, $options: 'i' }, _id: { $ne: excludeUserId } });
  return !!user;
};

userSchema.methods.isPasswordMatch = async function (password) {
  const user = this;
  return bcrypt.compare(password, user.password);
};

const User = mongoose.model('User', userSchema);

module.exports = User;
