const mongoose = require('mongoose');
// const Async = require('async');
const { deleteFileByBlobName } = require('../config/azure.setup');
const logger = require('../config/logger');

const fileSchema = new mongoose.Schema(
  {
    filename: {
      type: String,
      required: true,
      trim: true,
    },

    url: {
      type: String,
      required: true,
      trim: true,
    },

    containerName: {
      type: String,
      required: true,
      trim: true,
    },
    containingFolder: {
      type: String,
      trim: true,
    },

    thumbnail: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'File',
    },

    metadata: {
      height: { type: Number },
      width: { type: Number },
      rotation: { type: Number },
    },
  },
  {
    timestamps: true,
  },
);

// Delete a file file from azure when deleted from database
fileSchema.pre('remove', async function (next) {
  const file = this;
  try {
    deleteFileByBlobName(file.containerName, file.filename);
    logger.info(`File deleted from Azure. Container: ${file.containerName}, Blob: ${file.filename}`);
    next();
  } catch (error) {
    logger.error('Error deleting file from Azure:', error);
    next(error);
  }
});

fileSchema.pre('deleteOne', async function (next) {
  const file = this;
  try {
    deleteFileByBlobName(file.containerName, file.filename);
    next();
  } catch (error) {
    logger.error('Error deleting file from Azure:', error);
    next(error);
  }
});

// fileSchema.post(['find', 'findOne'], async (docs) => {
//   // eslint-disable-next-line global-require
//   const { generateSasUrlHelper } = require('../services/file.service');
//   if (Array.isArray(docs)) {
//     await Async.eachOfSeries(docs, async (doc) => {
//       // eslint-disable-next-line no-param-reassign
//       doc.url = await generateSasUrlHelper(doc);
//     });
//   } else if (docs) {
//     // eslint-disable-next-line no-param-reassign
//     docs.url = await generateSasUrlHelper(docs);
//   }
// });

const File = mongoose.model('File', fileSchema);

module.exports = File;
